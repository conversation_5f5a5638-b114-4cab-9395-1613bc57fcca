package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

import java.util.Collection;

/**
 * 业务数据上传请求VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineDataUploadRequestVO {
    
    /**
     * 选择需要上传同步的采样单id集合
     */
    private Collection<String> receiveIds;
    
    /**
     * 所属机构编码
     */
    private String orgCode;
    
    /**
     * 所属机构
     */
    private String orgId;
}
