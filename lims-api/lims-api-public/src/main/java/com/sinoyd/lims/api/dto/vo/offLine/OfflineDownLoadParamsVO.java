package com.sinoyd.lims.api.dto.vo.offLine;

import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import lombok.Data;

import java.util.Collection;

/**
 * 下载接口过程传参VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineDownLoadParamsVO {

    /**
     * 采样单id集合
     */
    private Collection<String> receiveIds;

    /**
     * 采样单集合
     */
    private Collection<DtoReceiveSampleRecord> receiveSampleRecords;

    /**
     * 点位id集合
     */
    private Collection<String> folderIds;

    /**
     * 点位集合
     */
    private Collection<DtoSampleFolder> sampleFolders;

    /**
     * 样品id
     */
    private Collection<String> sampleIds;

    /**
     * 样品集合
     */
    private Collection<DtoSample> samples;
}
