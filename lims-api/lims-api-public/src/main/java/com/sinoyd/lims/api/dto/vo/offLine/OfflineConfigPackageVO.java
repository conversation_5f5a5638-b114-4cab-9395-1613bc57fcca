package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 离线配置数据包封装类VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineConfigPackageVO {
    
    /**
     * 数据包版本
     */
    private String packageVersion;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 所属机构
     */
    private String orgId;
    
    // 4个配置表数据
    
    /**
     * 样品分组类型数据
     */
    private List<OffLineSampleGroupTypeVO> sampleGroupTypes;
    
    /**
     * 样品分组类型测试项目数据
     */
    private List<OffLineSampleGroupTypeTestVO> sampleGroupTypeTests;
    
    /**
     * 样品类型数据
     */
    private List<OffLineSampleTypeVO> sampleTypes;
    
    /**
     * 人员数据
     */
    private List<OffLinePersonVO> persons;
}
