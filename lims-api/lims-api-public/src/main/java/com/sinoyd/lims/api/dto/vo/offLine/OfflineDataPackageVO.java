package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 离线业务数据包封装类VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineDataPackageVO {
    
    /**
     * 采样单ID
     */
    private String receiveId;
    
    /**
     * 数据包版本
     */
    private String packageVersion;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 所属机构
     */
    private String orgId;
    
    // 11个业务表数据
    
    /**
     * 采样记录单数据
     */
    private List<OffLineSampleRecordVO> sampleRecords;
    
    /**
     * 点位数据
     */
    private List<OffLineSampleFolderVO> sampleFolders;
    
    /**
     * 点位签到数据
     */
    private List<OffLineFolderSignVO> folderSigns;
    
    /**
     * 样品数据
     */
    private List<OffLineSampleItemListVO> sampleItems;
    
    /**
     * 分析数据
     */
    private List<OffLineAnalyseDataVO> analyseData;
    
    /**
     * 样品分组数据
     */
    private List<OffLineSampleGroupVO> sampleGroups;
    
    /**
     * 样品分组测试项目数据
     */
    private List<OffLineSampleGroupTestVO> sampleGroupTests;
    
    /**
     * 公共参数数据
     */
    private List<OffLinePublicParamsDataVO> publicParams;
    
    /**
     * 点位参数数据
     */
    private List<OffLineFolderParamsDataVO> folderParams;
    
    /**
     * 样品参数数据
     */
    private List<OffLineSampleParamsDataVO> sampleParams;
    
    /**
     * 附件数据
     */
    private List<OffLineDocumentVO> documents;
}
