package com.sinoyd.lims.impl.service.offline.strategy;

import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;

import java.util.List;

/**
 * 配置数据转换抽象基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public abstract class AbstractConfigDataStrategy<T> implements IConfigDataStrategy<T> {
    
    /**
     * 通用的配置数据查询逻辑
     *
     * @param orgId 机构ID
     * @return 源数据列表
     */
    protected abstract List<Object> queryConfigData(String orgId);
    
    /**
     * 通用的数据映射逻辑
     *
     * @param sourceData 源数据
     * @return 配置数据
     */
    protected abstract T mapToConfigData(Object sourceData);
}
