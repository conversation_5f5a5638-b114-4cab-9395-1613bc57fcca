package com.sinoyd.lims.impl.service.offline;

import com.sinoyd.lims.api.service.offline.IConversionStrategyFactory;
import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 转换策略工厂实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Component
public class ConversionStrategyFactoryImpl implements IConversionStrategyFactory {
    
    /**
     * 业务数据转换策略映射
     */
    private final Map<String, IDataConversionStrategy> dataStrategies = new ConcurrentHashMap<>();
    
    /**
     * 配置数据转换策略映射
     */
    private final Map<String, IConfigDataStrategy> configStrategies = new ConcurrentHashMap<>();
    
    /**
     * 获取业务数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    @Override
    public IDataConversionStrategy getDataConversionStrategy(String tableName) {
        return dataStrategies.get(tableName);
    }
    
    /**
     * 获取配置数据转换策略
     *
     * @param tableName 表名
     * @return 配置数据转换策略
     */
    @Override
    public IConfigDataStrategy getConfigDataStrategy(String tableName) {
        return configStrategies.get(tableName);
    }
    
    /**
     * 注册业务数据转换策略
     *
     * @param tableName 表名
     * @param strategy 转换策略
     */
    @Override
    public void registerDataConversionStrategy(String tableName, IDataConversionStrategy strategy) {
        dataStrategies.put(tableName, strategy);
    }
    
    /**
     * 注册配置数据转换策略
     *
     * @param tableName 表名
     * @param strategy 转换策略
     */
    @Override
    public void registerConfigDataStrategy(String tableName, IConfigDataStrategy strategy) {
        configStrategies.put(tableName, strategy);
    }
}
