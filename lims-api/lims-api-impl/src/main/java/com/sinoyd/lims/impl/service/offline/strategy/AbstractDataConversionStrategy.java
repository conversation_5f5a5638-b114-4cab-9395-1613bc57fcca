package com.sinoyd.lims.impl.service.offline.strategy;

import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;

/**
 * 业务数据转换抽象基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public abstract class AbstractDataConversionStrategy<T> implements IDataConversionStrategy<T> {
    
    /**
     * 通用的数据转换逻辑
     *
     * @param onlineData 在线数据
     * @return 离线数据
     */
    protected abstract T mapToOfflineData(Object onlineData);
    
    /**
     * 通用的数据验证逻辑
     *
     * @param offlineData 离线数据
     * @return 是否验证通过
     */
    protected abstract boolean validateOfflineData(T offlineData);
    
    /**
     * 通用的冲突检测逻辑
     *
     * @param offlineData 离线数据
     * @param onlineData 在线数据
     * @return 冲突信息
     */
    protected abstract ConflictInfo checkDataConflict(T offlineData, Object onlineData);
    
    /**
     * 冲突信息内部类
     */
    protected static class ConflictInfo {
        private boolean hasConflict;
        private String conflictMessage;
        
        public ConflictInfo(boolean hasConflict, String conflictMessage) {
            this.hasConflict = hasConflict;
            this.conflictMessage = conflictMessage;
        }
        
        public boolean isHasConflict() {
            return hasConflict;
        }
        
        public String getConflictMessage() {
            return conflictMessage;
        }
    }
}
