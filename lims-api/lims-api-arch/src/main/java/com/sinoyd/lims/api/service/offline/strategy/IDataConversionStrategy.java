package com.sinoyd.lims.api.service.offline.strategy;

import com.sinoyd.lims.api.dto.vo.offLine.OfflineCheckVO;
import com.sinoyd.lims.api.dto.vo.offLine.OfflineDownLoadParamsVO;

import java.util.List;

/**
 * 业务数据双向转换策略接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IDataConversionStrategy<T> {
    
    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    String getSupportedTableName();
    
    /**
     * 在线数据转换为离线数据（下载）
     *
     * @param paramsVO 下载参数
     * @return 离线数据列表
     */
    List<T> convertToOfflineData(OfflineDownLoadParamsVO paramsVO);
    
    /**
     * 离线数据转换为在线数据（上传）
     *
     * @param offlineDataList 离线数据列表
     */
    void convertToOnlineData(List<T> offlineDataList);
    
    /**
     * 检测数据冲突
     *
     * @param offlineDataList 离线数据列表
     * @return 校验结果列表
     */
    List<OfflineCheckVO> check(List<T> offlineDataList);
}
