package com.sinoyd.lims.api.service.offline.strategy;

import java.util.List;

/**
 * 配置数据转换策略接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IConfigDataStrategy<T> {
    
    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    String getSupportedTableName();
    
    /**
     * 获取配置数据（全量下载）
     *
     * @param orgId 机构ID
     * @return 配置数据列表
     */
    List<T> getAllConfigData(String orgId);
}
