package com.sinoyd.lims.api.service.offline;

import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;

/**
 * 转换策略工厂接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IConversionStrategyFactory {
    
    /**
     * 获取业务数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    IDataConversionStrategy getDataConversionStrategy(String tableName);
    
    /**
     * 获取配置数据转换策略
     *
     * @param tableName 表名
     * @return 配置数据转换策略
     */
    IConfigDataStrategy getConfigDataStrategy(String tableName);
    
    /**
     * 注册业务数据转换策略
     *
     * @param tableName 表名
     * @param strategy 转换策略
     */
    void registerDataConversionStrategy(String tableName, IDataConversionStrategy strategy);
    
    /**
     * 注册配置数据转换策略
     *
     * @param tableName 表名
     * @param strategy 转换策略
     */
    void registerConfigDataStrategy(String tableName, IConfigDataStrategy strategy);
}
