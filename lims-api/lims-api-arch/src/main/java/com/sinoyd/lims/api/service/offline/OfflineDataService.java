package com.sinoyd.lims.api.service.offline;

import com.sinoyd.lims.api.dto.vo.offLine.OfflineDataDownloadRequestVO;
import com.sinoyd.lims.api.dto.vo.offLine.OfflineDataResponseVO;
import com.sinoyd.lims.api.dto.vo.offLine.OfflineDataUploadRequestVO;

/**
 * 离线数据转换服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface OfflineDataService {
    
    /**
     * 下载业务数据包
     *
     * @param request 下载请求参数
     * @return 离线数据响应
     */
    OfflineDataResponseVO downloadBusinessData(OfflineDataDownloadRequestVO request);
    
    /**
     * 上传业务数据包
     *
     * @param request 上传请求参数
     * @return 离线数据响应
     */
    OfflineDataResponseVO uploadBusinessData(OfflineDataUploadRequestVO request);
    
    /**
     * 下载配置数据包
     *
     * @param request 配置下载请求参数
     * @return 离线数据响应
     */
    OfflineDataResponseVO downloadConfigData(OfflineDataDownloadRequestVO request);
}
